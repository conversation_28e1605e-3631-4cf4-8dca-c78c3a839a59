# frozen_string_literal: true

require "sentry/rails/logger/active_record_subscriber"

module Sentry
  module Rails
    class Logger
      class << self
        # Subscribe to tracing events for structured logging
        def subscribe_tracing_events
          return unless Sentry.configuration.rails.structured_logging.enabled?
          return unless Sentry.configuration.enable_logs

          attach_to = Sentry.configuration.rails.structured_logging.attach_to

          if attach_to.include?(:active_record)
            ActiveRecordSubscriber.subscribe!
          end
        rescue => e
          Sentry.configuration.sdk_logger.error("Failed to subscribe to tracing events: #{e.message}")
          Sentry.configuration.sdk_logger.error(e.backtrace.join("\n"))
        end

        # Unsubscribe from tracing events
        def unsubscribe_tracing_events
          ActiveRecordSubscriber.unsubscribe! if defined?(ActiveRecordSubscriber)
        end
      end
    end
  end
end
