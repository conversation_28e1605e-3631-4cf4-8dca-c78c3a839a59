# frozen_string_literal: true

module Sentry
  module Rails
    class Logger
      class ActiveRecordSubscriber
        EVENT_NAMES = ["sql.active_record"].freeze
        EXCLUDED_EVENTS = ["SCHEMA", "TRANSACTION"].freeze

        class << self
          def subscribe!
            return if @subscribed

            @subscribers = EVENT_NAMES.map do |event_name|
              ActiveSupport::Notifications.subscribe(event_name) do |name, started, finished, unique_id, payload|
                handle_event(name, started, finished, unique_id, payload)
              end
            end

            @subscribed = true
          rescue => e
            Sentry.configuration.sdk_logger.error("Failed to subscribe to ActiveRecord events: #{e.message}")
            Sentry.configuration.sdk_logger.error(e.backtrace.join("\n"))
          end

          def unsubscribe!
            return unless @subscribed

            @subscribers&.each do |subscriber|
              ActiveSupport::Notifications.unsubscribe(subscriber)
            end

            @subscribers = nil
            @subscribed = false
          end

          private

          def handle_event(name, started, finished, unique_id, payload)
            # Skip Rails' internal events and excluded events
            return if name.start_with?("!")
            return if EXCLUDED_EVENTS.include?(payload[:name])

            # Calculate duration in milliseconds
            duration = ((finished - started) * 1000).round(2)

            # Extract relevant data from payload
            sql = payload[:sql]
            statement_name = payload[:name]
            cached = payload.fetch(:cached, false)
            connection_id = payload[:connection_id]

            # Get database configuration if available
            db_config = extract_db_config(payload)

            # Log the database query using Sentry's structured logger
            log_database_query(
              sql: sql,
              duration_ms: duration,
              statement_name: statement_name,
              cached: cached,
              connection_id: connection_id,
              db_config: db_config
            )
          end

          def extract_db_config(payload)
            connection = payload[:connection]

            if payload[:connection_id] && !connection
              # Fallback to base connection on Rails < 6.0.0
              connection = ActiveRecord::Base.connection_pool.connections.find do |conn|
                conn.object_id == payload[:connection_id]
              end
            end

            return nil unless connection

            if connection.pool.respond_to?(:db_config)
              connection.pool.db_config.configuration_hash
            elsif connection.pool.respond_to?(:spec)
              connection.pool.spec.config
            end
          rescue => e
            # Silently handle any errors in extracting db config
            Sentry.configuration.sdk_logger.debug("Failed to extract db config: #{e.message}")
            nil
          end

          def log_database_query(sql:, duration_ms:, statement_name:, cached:, connection_id:, db_config:)
            # Prepare structured data for the log
            attributes = {
              sql: sql,
              duration_ms: duration_ms,
              cached: cached
            }

            attributes[:statement_name] = statement_name if statement_name
            attributes[:connection_id] = connection_id if connection_id

            if db_config
              attributes[:db_system] = db_config[:adapter] if db_config[:adapter]
              attributes[:db_name] = db_config[:database] if db_config[:database]
              attributes[:server_address] = db_config[:host] if db_config[:host]
              attributes[:server_port] = db_config[:port] if db_config[:port]
              attributes[:server_socket_address] = db_config[:socket] if db_config[:socket]
            end

            # Log using Sentry's structured logger
            message = if statement_name && statement_name != "SQL"
              "Database query: #{statement_name}"
            else
              "Database query"
            end

            # Use info level for normal queries, warn for slow queries (>1000ms)
            level = duration_ms > 1000 ? :warn : :info

            Sentry.logger.public_send(level, message, **attributes)
          rescue => e
            # Silently handle any errors in logging to avoid breaking the application
            Sentry.configuration.sdk_logger.debug("Failed to log database query: #{e.message}")
          end
        end
      end
    end
  end
end
