# frozen_string_literal: true

require "spec_helper"

RSpec.describe Sentry::Rails::Logger, type: :request do
  before do
    expect(described_class).to receive(:subscribe_tracing_events).and_call_original

    make_basic_app do |config|
      config.traces_sample_rate = 1.0
      config.rails.structured_logging = true
      config.rails.structured_logging.attach_to = [:active_record]
    end
  end

  it "logs" do
    # TODO
  end
end
